/**
 * 数据安全验证器
 * 用于检测和过滤敏感信息
 */

// 敏感数据模式
const SENSITIVE_PATTERNS = [
  // 信用卡号
  /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g,
  // 邮箱地址
  /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
  // 电话号码
  /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g,
  // SSN (美国社会安全号)
  /\b\d{3}-\d{2}-\d{4}\b/g,
  // IP地址
  /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g,
  // URL
  /https?:\/\/[^\s]+/g,
]

// 配置常量
export const SECURITY_CONFIG = {
  MAX_TEXT_LENGTH: 5000,
  MIN_TEXT_LENGTH: 1,
  ALLOWED_PROTOCOLS: ['https:', 'http:'], // http: 仅用于开发环境
} as const

/**
 * 数据安全过滤器
 */
export class DataSecurityFilter {
  /**
   * 检查文本是否包含敏感数据
   */
  static containsSensitiveData(text: string): boolean {
    return SENSITIVE_PATTERNS.some(pattern => {
      pattern.lastIndex = 0 // 重置正则表达式状态
      return pattern.test(text)
    })
  }

  /**
   * 掩码敏感数据
   */
  static maskSensitiveData(text: string): string {
    let maskedText = text
    SENSITIVE_PATTERNS.forEach(pattern => {
      pattern.lastIndex = 0 // 重置正则表达式状态
      maskedText = maskedText.replace(pattern, '[REDACTED]')
    })
    return maskedText
  }

  /**
   * 获取敏感数据匹配项
   */
  static getSensitiveMatches(text: string): string[] {
    const matches: string[] = []
    SENSITIVE_PATTERNS.forEach(pattern => {
      pattern.lastIndex = 0 // 重置正则表达式状态
      let match
      while ((match = pattern.exec(text)) !== null) {
        matches.push(match[0])
      }
    })
    return matches
  }
}

/**
 * 文本验证器
 */
export class TextValidator {
  /**
   * 验证文本长度
   */
  static validateLength(text: string): { valid: boolean; error?: string } {
    if (text.length < SECURITY_CONFIG.MIN_TEXT_LENGTH) {
      return { valid: false, error: 'Text is too short' }
    }
    
    if (text.length > SECURITY_CONFIG.MAX_TEXT_LENGTH) {
      return { 
        valid: false, 
        error: `Text is too long (max ${SECURITY_CONFIG.MAX_TEXT_LENGTH} characters)` 
      }
    }
    
    return { valid: true }
  }

  /**
   * 验证文本内容
   */
  static validateContent(text: string): { valid: boolean; error?: string } {
    // 检查是否为空或只包含空白字符
    if (!text.trim()) {
      return { valid: false, error: 'Text cannot be empty' }
    }

    // 检查是否包含恶意脚本
    if (this.containsScript(text)) {
      return { valid: false, error: 'Text contains potentially malicious content' }
    }

    return { valid: true }
  }

  /**
   * 检查是否包含脚本标签
   */
  private static containsScript(text: string): boolean {
    const scriptPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
    ]
    
    return scriptPatterns.some(pattern => {
      pattern.lastIndex = 0
      return pattern.test(text)
    })
  }

  /**
   * 综合验证
   */
  static validate(text: string): { valid: boolean; error?: string } {
    // 长度验证
    const lengthResult = this.validateLength(text)
    if (!lengthResult.valid) {
      return lengthResult
    }

    // 内容验证
    const contentResult = this.validateContent(text)
    if (!contentResult.valid) {
      return contentResult
    }

    return { valid: true }
  }
}

/**
 * 环境安全检查器
 */
export class EnvironmentValidator {
  /**
   * 检查是否在安全环境中运行
   */
  static isSecureEnvironment(): boolean {
    // 开发环境允许 HTTP
    if (typeof window !== 'undefined') {
      const isLocalhost = window.location.hostname === 'localhost' || 
                         window.location.hostname === '127.0.0.1'
      
      if (isLocalhost) {
        return true
      }
      
      // 生产环境必须使用 HTTPS
      return window.location.protocol === 'https:'
    }
    
    return true // 服务器端环境
  }

  /**
   * 获取环境信息
   */
  static getEnvironmentInfo(): {
    protocol: string
    hostname: string
    isSecure: boolean
    isLocalhost: boolean
  } {
    if (typeof window === 'undefined') {
      return {
        protocol: 'server',
        hostname: 'server',
        isSecure: true,
        isLocalhost: false,
      }
    }

    const isLocalhost = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1'

    return {
      protocol: window.location.protocol,
      hostname: window.location.hostname,
      isSecure: this.isSecureEnvironment(),
      isLocalhost,
    }
  }
}

/**
 * 安全翻译验证器
 */
export class TranslationSecurityValidator {
  /**
   * 翻译前的安全检查
   */
  static validateForTranslation(text: string): { 
    valid: boolean
    error?: string
    warnings?: string[]
  } {
    const warnings: string[] = []

    // 环境检查
    if (!EnvironmentValidator.isSecureEnvironment()) {
      return { 
        valid: false, 
        error: 'Translation service requires a secure environment (HTTPS)' 
      }
    }

    // 文本验证
    const textValidation = TextValidator.validate(text)
    if (!textValidation.valid) {
      return textValidation
    }

    // 敏感数据检查
    if (DataSecurityFilter.containsSensitiveData(text)) {
      const matches = DataSecurityFilter.getSensitiveMatches(text)
      return { 
        valid: false, 
        error: `Text contains sensitive information that cannot be translated: ${matches.slice(0, 3).join(', ')}${matches.length > 3 ? '...' : ''}` 
      }
    }

    // 检查文本长度警告
    if (text.length > 1000) {
      warnings.push('Large text may take longer to translate')
    }

    return { valid: true, warnings }
  }
}
