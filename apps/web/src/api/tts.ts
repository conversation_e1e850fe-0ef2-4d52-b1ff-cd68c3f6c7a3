import axios from 'axios'

export interface VoiceInfo {
  id: string
  name: string
  language: string
  gender: string
  sample_rate?: number
  provider?: string
}

export interface TTSRequest {
  text: string
  voice?: string
  speed?: number
  pitch?: number
  output_format?: string
}

const API_BASE = (import.meta as any).env?.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'

export async function fetchVoices(): Promise<VoiceInfo[]> {
  const url = `${API_BASE}/tts/voices`
  const res = await axios.get(url)
  // expected: { voices: [...] }
  return res.data.voices || []
}

export async function synthesizeStream(payload: TTSRequest): Promise<Blob> {
  const url = `${API_BASE}/tts/synthesize-stream`
  const res = await axios.post(url, payload, { responseType: 'blob' })
  return res.data as Blob
}
