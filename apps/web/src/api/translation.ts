import { 
  defaultTranslationService, 
  TranslationOptions, 
  TranslationResult,
  PureTranslateProviderNames 
} from '../services/translation'

/**
 * 翻译文本的API函数
 */
export async function translateText(
  text: string,
  fromLang: string = 'auto',
  toLang: string = 'en',
  provider: PureTranslateProviderNames = 'google'
): Promise<TranslationResult> {
  try {
    const result = await defaultTranslationService.translate(text, {
      fromLang,
      toLang,
      provider,
      useCache: true,
    })
    
    return result
  } catch (error) {
    console.error('Translation failed:', error)
    throw new Error(`Translation failed: ${(error as Error).message}`)
  }
}

/**
 * 批量翻译文本
 */
export async function translateBatch(
  texts: string[],
  fromLang: string = 'auto',
  toLang: string = 'en',
  provider: PureTranslateProviderNames = 'google'
): Promise<TranslationResult[]> {
  try {
    const results = await defaultTranslationService.translateBatch(texts, {
      fromLang,
      toLang,
      provider,
      useCache: true,
    })
    
    return results
  } catch (error) {
    console.error('Batch translation failed:', error)
    throw new Error(`Batch translation failed: ${(error as Error).message}`)
  }
}

/**
 * 检测文本语言
 */
export async function detectLanguage(text: string): Promise<string> {
  try {
    return await defaultTranslationService.detectLanguage(text)
  } catch (error) {
    console.error('Language detection failed:', error)
    throw new Error(`Language detection failed: ${(error as Error).message}`)
  }
}

/**
 * 获取翻译服务统计信息
 */
export function getTranslationStats() {
  return {
    cacheSize: defaultTranslationService.getCacheSize(),
    queueStats: defaultTranslationService.getQueueStats(),
  }
}

/**
 * 清除翻译缓存
 */
export function clearTranslationCache(): void {
  defaultTranslationService.clearCache()
}

/**
 * 支持的翻译提供商列表
 */
export const SUPPORTED_PROVIDERS: PureTranslateProviderNames[] = ['google', 'microsoft', 'deeplx']

/**
 * 常用语言列表（与UI页面保持一致）
 */
export const COMMON_LANGUAGES = [
  { code: 'auto', name: 'Auto Detect' },
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' },
  { code: 'ja', name: '日本語' },
  { code: 'ko', name: '한국어' },
  { code: 'es', name: 'Español' },
  { code: 'fr', name: 'Français' },
  { code: 'de', name: 'Deutsch' },
  { code: 'ru', name: 'Русский' },
  { code: 'pt', name: 'Português' },
  { code: 'it', name: 'Italiano' },
  { code: 'ar', name: 'العربية' },
  { code: 'hi', name: 'हिन्दी' },
  { code: 'th', name: 'ไทย' },
  { code: 'vi', name: 'Tiếng Việt' },
  { code: 'tr', name: 'Türkçe' },
  { code: 'nl', name: 'Nederlands' },
  { code: 'pl', name: 'Polski' },
  { code: 'sv', name: 'Svenska' },
  { code: 'da', name: 'Dansk' },
]
