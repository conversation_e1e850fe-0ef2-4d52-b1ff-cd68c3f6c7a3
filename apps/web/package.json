{"name": "@reading-platform/web", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@tanstack/react-query": "^5.85.3", "axios": "^1.7.9", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "lucide-react": "^0.468.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@reading-platform/eslint-config": "workspace:*", "@reading-platform/typescript-config": "workspace:*", "@tailwindcss/vite": "^4.1.12", "@types/crypto-js": "^4.2.2", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "tailwindcss": "^4.1.12", "typescript": "^5.7.2", "vite": "^7.1.2"}}