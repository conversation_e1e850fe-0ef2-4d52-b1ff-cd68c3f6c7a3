"""
Text-to-Speech endpoints.
"""

from typing import Optional

from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Response
from pydantic import BaseModel, Field

from app.core.config import settings
from app.services.tts import TTSService

router = APIRouter()


class TTSRequest(BaseModel):
    """Text-to-Speech request model."""
    
    text: str = Field(..., min_length=1, max_length=5000)
    voice: Optional[str] = Field(default=settings.DEFAULT_TTS_VOICE)
    speed: Optional[float] = Field(default=settings.DEFAULT_TTS_SPEED, ge=0.5, le=2.0)
    pitch: Optional[float] = Field(default=settings.DEFAULT_TTS_PITCH, ge=0.5, le=2.0)
    output_format: Optional[str] = Field(default=settings.TTS_OUTPUT_FORMAT)


class TTSResponse(BaseModel):
    """Text-to-Speech response model."""
    
    text: str
    voice: str
    speed: float
    pitch: float
    output_format: str
    audio_url: str
    duration: Optional[float] = None


class VoiceInfo(BaseModel):
    """Voice information model."""
    
    id: str
    name: str
    language: str
    gender: str
    sample_rate: int


@router.post("/synthesize", response_model=TTSResponse)
async def synthesize_speech(request: TTSRequest):
    """
    Convert text to speech.
    
    Args:
        request: TTS request containing text and voice preferences
        
    Returns:
        TTSResponse: Audio file information and metadata
        
    Raises:
        HTTPException: If speech synthesis fails
    """
    try:
        tts_service = TTSService()
        result = await tts_service.synthesize(
            text=request.text,
            voice=request.voice,
            speed=request.speed,
            pitch=request.pitch,
            output_format=request.output_format,
        )
        
        return TTSResponse(
            text=request.text,
            voice=request.voice,
            speed=request.speed,
            pitch=request.pitch,
            output_format=request.output_format,
            audio_url=result["audio_url"],
            duration=result.get("duration"),
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Speech synthesis failed: {str(e)}"
        )


@router.post("/synthesize-stream")
async def synthesize_speech_stream(request: TTSRequest):
    """
    Convert text to speech and return audio stream.
    
    Args:
        request: TTS request containing text and voice preferences
        
    Returns:
        Response: Audio file as streaming response
        
    Raises:
        HTTPException: If speech synthesis fails
    """
    try:
        tts_service = TTSService()
        audio_data = await tts_service.synthesize_stream(
            text=request.text,
            voice=request.voice,
            speed=request.speed,
            pitch=request.pitch,
            output_format=request.output_format,
        )
        
        media_type = f"audio/{request.output_format}"
        return Response(
            content=audio_data,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename=speech.{request.output_format}"
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Speech synthesis failed: {str(e)}"
        )


@router.get("/voices")
async def get_available_voices():
    """
    Get list of available voices for text-to-speech.
    
    Returns:
        dict: List of available voices with metadata
    """
    try:
        tts_service = TTSService()
        voices = await tts_service.get_available_voices()
        return {"voices": voices}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get available voices: {str(e)}"
        )


@router.get("/voices/{language}")
async def get_voices_by_language(language: str):
    """
    Get list of available voices for a specific language.
    
    Args:
        language: Language code (e.g., 'en-US', 'zh-CN')
        
    Returns:
        dict: List of available voices for the specified language
    """
    try:
        tts_service = TTSService()
        voices = await tts_service.get_voices_by_language(language)
        return {"language": language, "voices": voices}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get voices for language {language}: {str(e)}",
        )
