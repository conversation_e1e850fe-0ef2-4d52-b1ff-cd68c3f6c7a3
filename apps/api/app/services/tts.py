"""
Text-to-Speech service implementation using edge-tts.
"""

import io
import uuid
from typing import Any, Dict, List

import edge_tts
from app.core.config import settings


def _map_speed_to_rate(speed: float) -> str:
    """Map speed (0.5-2.0) to edge-tts rate string like "+20%" or "-30%"."""
    clamped = max(0.5, min(2.0, speed))
    percent = int(round((clamped - 1.0) * 100))
    sign = "+" if percent >= 0 else ""
    return f"{sign}{percent}%"


def _map_pitch_to_edge(pitch: float) -> str:
    """Map pitch (0.5-2.0) to edge-tts pitch string in Hz, e.g., "+0Hz", "+50Hz", "-50Hz"."""
    clamped = max(0.5, min(2.0, pitch))
    # Map 1.0 -> 0Hz, 0.5 -> -50Hz, 2.0 -> +50Hz (linear approx)
    hz = int(round((clamped - 1.0) * 100)) // 2
    sign = "+" if hz >= 0 else ""
    return f"{sign}{hz}Hz"


class TTSService:
    """Text-to-Speech service backed by Microsoft Edge online TTS via edge-tts."""

    def __init__(self):
        pass

    async def synthesize(
        self,
        text: str,
        voice: str = "en-US-AriaNeural",
        speed: float = 1.0,
        pitch: float = 1.0,
        output_format: str = "mp3",
    ) -> Dict[str, Any]:
        """
        Convert text to speech and return metadata. Use synthesize_stream to get bytes.
        """
        audio_id = str(uuid.uuid4())
        audio_filename = f"{audio_id}.{output_format}"
        audio_url = f"/api/v1/tts/audio/{audio_filename}"

        word_count = len(text.split())
        duration = (word_count / 150) * 60 / max(0.5, min(2.0, speed))

        return {
            "audio_url": audio_url,
            "audio_filename": audio_filename,
            "duration": duration,
        }

    async def synthesize_stream(
        self,
        text: str,
        voice: str = "en-US-AriaNeural",
        speed: float = 1.0,
        pitch: float = 1.0,
        output_format: str = "mp3",
    ) -> bytes:
        """
        Convert text to speech and return audio data as bytes using edge-tts.
        """
        rate = _map_speed_to_rate(speed)
        pitch_s = _map_pitch_to_edge(pitch)

        communicate = edge_tts.Communicate(
            text=text,
            voice=voice,
            rate=rate,
            pitch=pitch_s,
        )

        buf = io.BytesIO()
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                buf.write(chunk["data"])
        return buf.getvalue()

    async def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available voices from edge-tts."""
        voices = await edge_tts.list_voices()
        results: List[Dict[str, Any]] = []
        for v in voices:
            results.append(
                {
                    "id": v.get("Name") or v.get("ShortName") or "",
                    "name": v.get("FriendlyName") or v.get("ShortName") or v.get("Name") or "",
                    "language": v.get("Locale") or v.get("LocaleName") or "",
                    "gender": (v.get("Gender") or "").lower(),
                    "sample_rate": 24000,
                    "provider": "edge-tts",
                }
            )
        return results

    async def get_voices_by_language(self, language: str) -> List[Dict[str, Any]]:
        all_voices = await self.get_available_voices()
        return [voice for voice in all_voices if voice["language"] == language]
